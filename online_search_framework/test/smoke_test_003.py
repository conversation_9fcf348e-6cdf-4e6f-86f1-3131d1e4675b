#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Online Search Framework 边界条件测试 003
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 参数边界值测试
2. 异常输入测试
3. 大数据量测试
4. 特殊字符测试
5. 超长查询测试
6. 无效参数测试
"""

import requests
import time
import json
import sys
import jwt
from typing import List, Dict, Any
from datetime import datetime, timedelta, timezone


class OnlineSearchBoundaryTest:
    """Online Search Framework 边界条件测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.search_url = f"{base_url}/cabin/web_search/v2"
        
        # JWT认证配置
        self.ak = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
        self.sk = "b8bb9b60-9b3d-4e68-be09-f1e6816794d4"
        self.token = self._create_access_token()
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        self.test_results = []
        
    def _create_access_token(self) -> str:
        """创建JWT访问令牌"""
        expire = datetime.now(timezone.utc) + timedelta(days=7)
        nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
        to_encode = {"iss": self.ak, "exp": expire, "nbf": nbf}
        encoded_jwt = jwt.encode(to_encode, self.sk, algorithm="HS256")
        return encoded_jwt
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def test_parameter_boundaries(self) -> bool:
        """测试1: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()
        
        test_cases = [
            {
                "desc": "最小k值",
                "payload": {"engine": "bing", "query": "测试", "k": 1, "stream": False, "detect": True}
            },
            {
                "desc": "最大k值",
                "payload": {"engine": "bing", "query": "测试", "k": 50, "stream": False, "detect": True}
            },
            {
                "desc": "零k值",
                "payload": {"engine": "bing", "query": "测试", "k": 0, "stream": False, "detect": True}
            },
            {
                "desc": "负数k值",
                "payload": {"engine": "bing", "query": "测试", "k": -1, "stream": False, "detect": True}
            },
            {
                "desc": "最短查询",
                "payload": {"engine": "bing", "query": "a", "k": 5, "stream": False, "detect": True}
            },
            {
                "desc": "空查询",
                "payload": {"engine": "bing", "query": "", "k": 5, "stream": False, "detect": True}
            }
        ]
        
        successful_cases = 0
        
        for case in test_cases:
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=case["payload"],
                    timeout=30
                )
                
                # 对于边界值测试，我们主要关注服务是否能正确处理而不崩溃
                if response.status_code in [200, 400, 422]:  # 200成功，400/422参数错误也是合理的
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 正确处理 (状态码: {response.status_code})")
                else:
                    print(f"   ✗ {case['desc']}: 异常状态码 {response.status_code}")
                    
            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 边界测试标准：至少80%的用例能正确处理
        success_threshold = len(test_cases) * 0.8
        performance_ok = successful_cases >= success_threshold
        
        message = f"边界测试通过 ({successful_cases}/{len(test_cases)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_special_characters(self) -> bool:
        """测试2: 特殊字符测试"""
        test_name = "特殊字符测试"
        start_time = time.time()
        
        special_queries = [
            "测试中文查询",
            "Test English Query",
            "测试 Mixed 语言 Query",
            "特殊符号!@#$%^&*()",
            "数字123456789",
            "emoji测试🔍🌟💡",
            "HTML标签<script>alert('test')</script>",
            "SQL注入'; DROP TABLE users; --",
            "JSON格式{\"test\": \"value\"}",
            "URL测试https://www.example.com"
        ]
        
        successful_queries = 0
        
        for i, query in enumerate(special_queries):
            payload = {
                "engine": "bing",
                "query": query,
                "k": 3,
                "stream": False,
                "detect": True
            }
            
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    successful_queries += 1
                    print(f"   ✓ 特殊字符{i+1}: 成功处理")
                else:
                    print(f"   ✗ 特殊字符{i+1}: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ 特殊字符{i+1}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 特殊字符测试标准：至少70%成功
        success_threshold = len(special_queries) * 0.7
        performance_ok = successful_queries >= success_threshold
        
        message = f"特殊字符测试通过 ({successful_queries}/{len(special_queries)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_long_queries(self) -> bool:
        """测试3: 超长查询测试"""
        test_name = "超长查询测试"
        start_time = time.time()
        
        # 生成不同长度的查询
        base_query = "这是一个测试查询，用于验证系统对长查询的处理能力。"
        
        long_queries = [
            base_query * 5,    # 约150字符
            base_query * 10,   # 约300字符
            base_query * 20,   # 约600字符
            base_query * 50,   # 约1500字符
            base_query * 100   # 约3000字符
        ]
        
        successful_queries = 0
        
        for i, query in enumerate(long_queries):
            payload = {
                "engine": "bing",
                "query": query,
                "k": 3,
                "stream": False,
                "detect": True
            }
            
            try:
                req_start = time.time()
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                req_duration = time.time() - req_start
                
                if response.status_code == 200:
                    successful_queries += 1
                    print(f"   ✓ 长查询{i+1} ({len(query)}字符): 成功 ({req_duration:.2f}s)")
                else:
                    print(f"   ✗ 长查询{i+1} ({len(query)}字符): 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ 长查询{i+1} ({len(query)}字符): 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 长查询测试标准：至少60%成功（长查询可能有限制）
        success_threshold = len(long_queries) * 0.6
        performance_ok = successful_queries >= success_threshold
        
        message = f"长查询测试通过 ({successful_queries}/{len(long_queries)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_invalid_parameters(self) -> bool:
        """测试4: 无效参数测试"""
        test_name = "无效参数测试"
        start_time = time.time()
        
        invalid_test_cases = [
            {
                "desc": "无效引擎",
                "payload": {"engine": "invalid_engine", "query": "测试", "k": 5, "stream": False, "detect": True}
            },
            {
                "desc": "缺少必需参数",
                "payload": {"k": 5, "stream": False, "detect": True}  # 缺少engine和query
            },
            {
                "desc": "错误数据类型",
                "payload": {"engine": "bing", "query": 123, "k": "invalid", "stream": "not_bool", "detect": True}
            },
            {
                "desc": "额外无效参数",
                "payload": {"engine": "bing", "query": "测试", "k": 5, "stream": False, "detect": True, "invalid_param": "test"}
            },
            {
                "desc": "空payload",
                "payload": {}
            }
        ]
        
        correctly_handled = 0
        
        for case in invalid_test_cases:
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=case["payload"],
                    timeout=30
                )
                
                # 对于无效参数，期望返回4xx错误码
                if response.status_code in [400, 422, 500]:
                    correctly_handled += 1
                    print(f"   ✓ {case['desc']}: 正确拒绝 (状态码: {response.status_code})")
                elif response.status_code == 200:
                    print(f"   ⚠ {case['desc']}: 意外接受了无效参数")
                else:
                    print(f"   ✗ {case['desc']}: 异常状态码 {response.status_code}")
                    
            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 无效参数测试标准：至少80%正确处理
        success_threshold = len(invalid_test_cases) * 0.8
        performance_ok = correctly_handled >= success_threshold
        
        message = f"无效参数测试通过 ({correctly_handled}/{len(invalid_test_cases)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_concurrent_boundary_requests(self) -> bool:
        """测试5: 并发边界请求测试"""
        test_name = "并发边界请求测试"
        start_time = time.time()
        
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        def boundary_request(request_id: int) -> Dict[str, Any]:
            """边界条件请求"""
            # 使用不同的边界条件
            boundary_payloads = [
                {"engine": "bing", "query": "a", "k": 1, "stream": False, "detect": True},
                {"engine": "google", "query": "测试" * 100, "k": 20, "stream": False, "detect": True},
                {"engine": "tencent", "query": "特殊符号!@#$%", "k": 5, "stream": True, "detect": True},
            ]
            
            payload = boundary_payloads[request_id % len(boundary_payloads)]
            
            req_start = time.time()
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=45
                )
                req_duration = time.time() - req_start
                
                return {
                    "request_id": request_id,
                    "success": response.status_code in [200, 400, 422],
                    "duration": req_duration,
                    "status_code": response.status_code
                }
            except Exception as e:
                req_duration = time.time() - req_start
                return {
                    "request_id": request_id,
                    "success": False,
                    "duration": req_duration,
                    "error": str(e)
                }
        
        # 并发边界测试：3个并发请求
        concurrent_requests = 3
        print(f"   执行 {concurrent_requests} 个并发边界请求...")
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(boundary_request, i) for i in range(concurrent_requests)]
            results = []
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                status = "✓" if result["success"] else "✗"
                print(f"   边界请求{result['request_id']}: {status} {result['duration']:.2f}s")
        
        duration = time.time() - start_time
        
        # 分析并发边界测试结果
        successful_requests = sum(1 for r in results if r["success"])
        success_rate = successful_requests / concurrent_requests
        
        # 并发边界测试标准：成功率 > 60%
        performance_ok = success_rate > 0.6
        
        message = f"并发边界测试成功率:{success_rate:.1%}"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_memory_stress(self) -> bool:
        """测试6: 内存压力测试"""
        test_name = "内存压力测试"
        start_time = time.time()
        
        # 快速连续请求测试内存处理
        rapid_requests = 10
        successful_requests = 0
        
        print(f"   执行 {rapid_requests} 个快速连续请求...")
        
        for i in range(rapid_requests):
            payload = {
                "engine": "bing",
                "query": f"内存测试查询{i}",
                "k": 3,
                "stream": False,
                "detect": True
            }
            
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    successful_requests += 1
                    print(f"   ✓ 快速请求{i+1}: 成功")
                else:
                    print(f"   ✗ 快速请求{i+1}: 失败 (状态码: {response.status_code})")
                    
                # 短暂间隔
                time.sleep(0.1)
                
            except Exception as e:
                print(f"   ✗ 快速请求{i+1}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 内存压力测试标准：成功率 > 70%
        success_rate = successful_requests / rapid_requests
        performance_ok = success_rate > 0.7
        
        message = f"内存压力测试成功率:{success_rate:.1%}"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def run_boundary_tests(self) -> bool:
        """运行所有边界条件测试"""
        print("=" * 80)
        print("🔬 Online Search Framework 边界条件测试 003")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_parameter_boundaries,
            self.test_special_characters,
            self.test_long_queries,
            self.test_invalid_parameters,
            self.test_concurrent_boundary_requests,
            self.test_memory_stress
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 边界条件测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 边界条件测试全部通过！服务健壮性良好")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分边界条件测试通过，服务健壮性基本满足要求")
        else:
            print("⚠️  多项边界条件测试失败，请提高服务健壮性")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    boundary_test = OnlineSearchBoundaryTest(base_url)
    success = boundary_test.run_boundary_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
