#!/bin/bash

# 设置变量
API_URL="http://127.0.0.1:8080/gac/web_search/v1/news_query"  # 替换为实际的API地址
QUERY="最近有什么热榜新文"               # 可替换为实际查询内容

# 构建 JSON payload
PAYLOAD='{
    "query": "'"$QUERY"'",
    "stream": false,
    "use_search_cache": false,
    "detect": false,
    "history": [
        
    ],
    "engine": "tencent",
    "location": {
        "lat": "25.09204",
        "lon": "104.8955"
    },
    "user_info": {
        "car_id": "fake_car_id",
        "user_id": "fake_user_id",
        "category": [
            "news_topic",
            "news_type"
        ]
    }
}'
# 使用 curl 发送请求
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD"