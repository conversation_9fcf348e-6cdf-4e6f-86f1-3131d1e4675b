#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Online Search Framework 快速冒烟测试
专门用于CI环境的简化版本，只测试最核心的功能

测试内容：
1. 健康检查测试
2. 基础搜索功能测试
3. 流式响应测试
"""

import requests
import time
import json
import sys
from typing import Dict, Any


class QuickSmokeTest:
    """快速冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/cabin/web_search/health"
        self.search_url = f"{base_url}/cabin/web_search/v2"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def test_health_endpoint(self) -> bool:
        """测试1: 健康检查端点测试"""
        test_name = "健康检查测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "ok":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False

    def test_basic_search(self) -> bool:
        """测试2: 基础搜索功能测试"""
        test_name = "基础搜索测试"
        start_time = time.time()

        payload = {
            "engine": "google",
            "query": "测试查询",
            "stream": False,
            "detect": True,
            "k": 3
        }
        
        try:
            response = requests.post(
                self.search_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 检查是否有答案内容或者返回码为0（成功）
                    if ("answer" in result and result["answer"]) or result.get("code") == 0:
                        answer_length = len(result.get("answer", ""))
                        self.log_test_result(test_name, True, f"搜索功能正常 (答案长度: {answer_length})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"搜索无结果 (code: {result.get('code', 'unknown')})", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "搜索响应JSON格式错误", duration)
                    return False
            else:
                error_msg = response.text[:100] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"搜索请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False
                
        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "搜索请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"搜索请求异常: {str(e)}", duration)
            return False

    def test_stream_response(self) -> bool:
        """测试3: 流式响应测试"""
        test_name = "流式响应测试"
        start_time = time.time()
        
        payload = {
            "engine": "google",
            "query": "简单测试",
            "stream": True,
            "detect": True,
            "k": 1
        }
        
        try:
            response = requests.post(
                self.search_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=20
            )
            
            if response.status_code == 200:
                chunks_received = 0
                
                # 读取流式数据
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks_received += 1
                        # 限制读取时间，避免无限等待
                        if time.time() - start_time > 15:
                            break
                
                duration = time.time() - start_time
                
                if chunks_received > 0:
                    self.log_test_result(test_name, True, f"流式响应正常 (接收{chunks_received}个数据块)", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "未接收到流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                self.log_test_result(test_name, False, f"流式请求失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"流式响应异常: {str(e)}", duration)
            return False

    def run_quick_tests(self) -> bool:
        """运行快速冒烟测试"""
        print("=" * 60)
        print("🚀 Online Search Framework 快速冒烟测试")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        # 执行测试序列
        tests = [
            self.test_health_endpoint,
            self.test_basic_search,
            self.test_stream_response
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 60)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 快速测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 快速冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.67:
            print("✅ 大部分快速测试通过，服务基本正常")
        else:
            print("⚠️  多项快速测试失败，请检查服务状态")

        print("=" * 60)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    quick_test = QuickSmokeTest(base_url)
    success = quick_test.run_quick_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
