#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Online Search Framework 错误处理测试 004
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 网络错误恢复测试
2. 超时处理测试
3. 认证错误测试
4. 服务异常恢复测试
5. 数据格式错误测试
6. 资源限制测试
"""

import requests
import time
import json
import sys
import jwt
from typing import List, Dict, Any
from datetime import datetime, timedelta, timezone


class OnlineSearchErrorTest:
    """Online Search Framework 错误处理测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.search_url = f"{base_url}/cabin/web_search/v2"
        
        # JWT认证配置
        self.ak = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
        self.sk = "b8bb9b60-9b3d-4e68-be09-f1e6816794d4"
        self.token = self._create_access_token()
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        self.test_results = []
        
    def _create_access_token(self) -> str:
        """创建JWT访问令牌"""
        expire = datetime.now(timezone.utc) + timedelta(days=7)
        nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
        to_encode = {"iss": self.ak, "exp": expire, "nbf": nbf}
        encoded_jwt = jwt.encode(to_encode, self.sk, algorithm="HS256")
        return encoded_jwt
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def test_authentication_errors(self) -> bool:
        """测试1: 认证错误测试"""
        test_name = "认证错误测试"
        start_time = time.time()
        
        auth_test_cases = [
            {
                "desc": "无认证头",
                "headers": {"Content-Type": "application/json"},
                "expected_codes": [401, 403]
            },
            {
                "desc": "无效token",
                "headers": {"Content-Type": "application/json", "Authorization": "Bearer invalid_token"},
                "expected_codes": [401, 403]
            },
            {
                "desc": "过期token",
                "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ0ZXN0IiwiZXhwIjoxNjAwMDAwMDAwfQ.invalid"},
                "expected_codes": [401, 403]
            },
            {
                "desc": "错误格式认证头",
                "headers": {"Content-Type": "application/json", "Authorization": "InvalidFormat token"},
                "expected_codes": [401, 403]
            }
        ]
        
        correctly_handled = 0
        
        payload = {
            "engine": "bing",
            "query": "测试查询",
            "stream": False,
            "detect": True,
            "k": 3
        }
        
        for case in auth_test_cases:
            try:
                response = requests.post(
                    self.search_url,
                    headers=case["headers"],
                    json=payload,
                    timeout=30
                )
                
                if response.status_code in case["expected_codes"]:
                    correctly_handled += 1
                    print(f"   ✓ {case['desc']}: 正确拒绝 (状态码: {response.status_code})")
                else:
                    print(f"   ✗ {case['desc']}: 未正确处理 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 认证错误测试标准：至少80%正确处理
        success_threshold = len(auth_test_cases) * 0.8
        performance_ok = correctly_handled >= success_threshold
        
        message = f"认证错误测试通过 ({correctly_handled}/{len(auth_test_cases)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_timeout_handling(self) -> bool:
        """测试2: 超时处理测试"""
        test_name = "超时处理测试"
        start_time = time.time()
        
        # 使用很短的超时时间来测试超时处理
        short_timeouts = [1, 2, 3]  # 1-3秒超时
        timeout_handled = 0
        
        payload = {
            "engine": "bing",
            "query": "这是一个可能需要较长时间处理的复杂查询，用于测试超时处理机制",
            "stream": False,
            "detect": True,
            "k": 10
        }
        
        for timeout_val in short_timeouts:
            try:
                req_start = time.time()
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=timeout_val
                )
                req_duration = time.time() - req_start
                
                # 如果在超时时间内完成，也算正常
                if response.status_code == 200:
                    timeout_handled += 1
                    print(f"   ✓ {timeout_val}s超时: 正常完成 ({req_duration:.2f}s)")
                else:
                    print(f"   ⚠ {timeout_val}s超时: 响应异常 (状态码: {response.status_code})")
                    
            except requests.exceptions.Timeout:
                timeout_handled += 1
                req_duration = time.time() - req_start
                print(f"   ✓ {timeout_val}s超时: 正确超时处理 ({req_duration:.2f}s)")
            except Exception as e:
                print(f"   ✗ {timeout_val}s超时: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 超时处理测试标准：至少60%正确处理
        success_threshold = len(short_timeouts) * 0.6
        performance_ok = timeout_handled >= success_threshold
        
        message = f"超时处理测试通过 ({timeout_handled}/{len(short_timeouts)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_malformed_requests(self) -> bool:
        """测试3: 格式错误请求测试"""
        test_name = "格式错误请求测试"
        start_time = time.time()
        
        malformed_cases = [
            {
                "desc": "无效JSON",
                "data": "invalid json data",
                "content_type": "application/json"
            },
            {
                "desc": "空请求体",
                "data": "",
                "content_type": "application/json"
            },
            {
                "desc": "错误Content-Type",
                "data": json.dumps({"engine": "bing", "query": "测试", "stream": False, "detect": True}),
                "content_type": "text/plain"
            },
            {
                "desc": "超大JSON",
                "data": json.dumps({"engine": "bing", "query": "x" * 10000, "stream": False, "detect": True}),
                "content_type": "application/json"
            }
        ]
        
        correctly_handled = 0
        
        for case in malformed_cases:
            try:
                headers = {
                    "Content-Type": case["content_type"],
                    "Authorization": f"Bearer {self.token}"
                }
                
                response = requests.post(
                    self.search_url,
                    headers=headers,
                    data=case["data"],
                    timeout=30
                )
                
                # 期望返回4xx错误码
                if response.status_code in [400, 422, 413, 415]:
                    correctly_handled += 1
                    print(f"   ✓ {case['desc']}: 正确拒绝 (状态码: {response.status_code})")
                else:
                    print(f"   ✗ {case['desc']}: 未正确处理 (状态码: {response.status_code})")
                    
            except Exception as e:
                # 连接错误也可能是正确的处理方式
                correctly_handled += 1
                print(f"   ✓ {case['desc']}: 连接拒绝 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 格式错误测试标准：至少75%正确处理
        success_threshold = len(malformed_cases) * 0.75
        performance_ok = correctly_handled >= success_threshold
        
        message = f"格式错误测试通过 ({correctly_handled}/{len(malformed_cases)})"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_service_recovery(self) -> bool:
        """测试4: 服务恢复能力测试"""
        test_name = "服务恢复能力测试"
        start_time = time.time()
        
        # 先发送一些可能导致错误的请求
        error_requests = [
            {"engine": "invalid", "query": "", "stream": False, "detect": True},
            {"engine": "bing", "query": None, "stream": False, "detect": True},
            {"engine": "bing", "query": "x" * 5000, "stream": False, "detect": True}
        ]
        
        print("   发送错误请求...")
        for i, payload in enumerate(error_requests):
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                print(f"   错误请求{i+1}: 状态码 {response.status_code}")
            except Exception as e:
                print(f"   错误请求{i+1}: 异常 - {str(e)}")
        
        # 短暂等待
        time.sleep(2)
        
        # 然后发送正常请求，测试服务是否能恢复
        recovery_successful = 0
        normal_requests = 3
        
        print("   测试服务恢复...")
        for i in range(normal_requests):
            payload = {
                "engine": "bing",
                "query": f"恢复测试查询{i+1}",
                "stream": False,
                "detect": True,
                "k": 3
            }
            
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "answer" in result:
                        recovery_successful += 1
                        print(f"   ✓ 恢复请求{i+1}: 成功")
                    else:
                        print(f"   ✗ 恢复请求{i+1}: 无答案")
                else:
                    print(f"   ✗ 恢复请求{i+1}: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ 恢复请求{i+1}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 服务恢复测试标准：至少60%的正常请求成功
        success_rate = recovery_successful / normal_requests
        performance_ok = success_rate >= 0.6
        
        message = f"服务恢复测试成功率:{success_rate:.1%}"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_resource_limits(self) -> bool:
        """测试5: 资源限制测试"""
        test_name = "资源限制测试"
        start_time = time.time()
        
        # 测试大量并发请求
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        def stress_request(request_id: int) -> Dict[str, Any]:
            """压力测试请求"""
            payload = {
                "engine": "bing",
                "query": f"压力测试{request_id}",
                "stream": False,
                "detect": True,
                "k": 5
            }
            
            req_start = time.time()
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                req_duration = time.time() - req_start
                
                return {
                    "request_id": request_id,
                    "success": response.status_code in [200, 429, 503],  # 包括限流响应
                    "duration": req_duration,
                    "status_code": response.status_code
                }
            except Exception as e:
                req_duration = time.time() - req_start
                return {
                    "request_id": request_id,
                    "success": False,
                    "duration": req_duration,
                    "error": str(e)
                }
        
        # 高并发测试：10个并发请求
        concurrent_requests = 10
        print(f"   执行 {concurrent_requests} 个高并发请求...")
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(stress_request, i) for i in range(concurrent_requests)]
            results = []
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                status = "✓" if result["success"] else "✗"
                status_info = result.get("status_code", "异常")
                print(f"   压力请求{result['request_id']}: {status} ({status_info}) {result['duration']:.2f}s")
        
        duration = time.time() - start_time
        
        # 分析资源限制测试结果
        successful_requests = sum(1 for r in results if r["success"])
        success_rate = successful_requests / concurrent_requests
        
        # 资源限制测试标准：成功率 > 50%（考虑到可能的限流）
        performance_ok = success_rate > 0.5
        
        message = f"资源限制测试成功率:{success_rate:.1%}"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_error_response_format(self) -> bool:
        """测试6: 错误响应格式测试"""
        test_name = "错误响应格式测试"
        start_time = time.time()
        
        # 发送会产生错误的请求，检查错误响应格式
        error_payload = {
            "engine": "nonexistent_engine",
            "query": "测试错误响应",
            "stream": False,
            "detect": True,
            "k": 5
        }
        
        try:
            response = requests.post(
                self.search_url,
                headers=self.headers,
                json=error_payload,
                timeout=30
            )
            
            duration = time.time() - start_time
            
            # 检查错误响应是否有合适的格式
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                    # 检查是否包含错误信息
                    has_error_info = any(key in error_data for key in ["error", "message", "detail", "code"])
                    
                    if has_error_info:
                        print(f"   ✓ 错误响应格式正确: {response.status_code}")
                        self.log_test_result(test_name, True, f"错误响应格式正确 (状态码: {response.status_code})", duration)
                        return True
                    else:
                        print(f"   ✗ 错误响应缺少错误信息")
                        self.log_test_result(test_name, False, "错误响应缺少错误信息", duration)
                        return False
                        
                except json.JSONDecodeError:
                    print(f"   ⚠ 错误响应不是JSON格式")
                    # 非JSON错误响应也可能是合理的
                    self.log_test_result(test_name, True, f"错误响应非JSON格式 (状态码: {response.status_code})", duration)
                    return True
            else:
                print(f"   ⚠ 预期错误请求返回成功状态码: {response.status_code}")
                self.log_test_result(test_name, False, f"预期错误请求返回成功: {response.status_code}", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"   ✗ 错误响应测试异常: {str(e)}")
            self.log_test_result(test_name, False, f"错误响应测试异常: {str(e)}", duration)
            return False

    def run_error_tests(self) -> bool:
        """运行所有错误处理测试"""
        print("=" * 80)
        print("🛠️ Online Search Framework 错误处理测试 004")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_authentication_errors,
            self.test_timeout_handling,
            self.test_malformed_requests,
            self.test_service_recovery,
            self.test_resource_limits,
            self.test_error_response_format
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 错误处理测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 错误处理测试全部通过！服务错误处理能力优秀")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分错误处理测试通过，服务错误处理能力良好")
        else:
            print("⚠️  多项错误处理测试失败，请改进服务错误处理机制")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    error_test = OnlineSearchErrorTest(base_url)
    success = error_test.run_error_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
