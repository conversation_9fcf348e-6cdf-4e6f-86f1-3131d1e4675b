#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Online Search Framework 性能测试 002
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 响应时间性能测试（10轮）
2. 并发性能测试
3. 不同引擎性能对比
4. 流式vs非流式性能对比
5. 缓存性能测试
"""

import requests
import time
import json
import sys
import jwt
import asyncio
import aiohttp
from typing import List, Dict, Any
from statistics import mean, median
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed


class OnlineSearchPerformanceTest:
    """Online Search Framework 性能测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.search_url = f"{base_url}/cabin/web_search/v2"
        
        # JWT认证配置
        self.ak = "146049b1-0a95-49a2-8856-5e5e35f0f9a6"
        self.sk = "b8bb9b60-9b3d-4e68-be09-f1e6816794d4"
        self.token = self._create_access_token()
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        self.test_results = []
        
    def _create_access_token(self) -> str:
        """创建JWT访问令牌"""
        expire = datetime.now(timezone.utc) + timedelta(days=7)
        nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
        to_encode = {"iss": self.ak, "exp": expire, "nbf": nbf}
        encoded_jwt = jwt.encode(to_encode, self.sk, algorithm="HS256")
        return encoded_jwt
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def extract_timing_info(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """从响应数据中提取时间信息"""
        timing_info = {}
        
        if isinstance(response_data, dict):
            time_cost = response_data.get("time_cost", {})
            if isinstance(time_cost, dict):
                # 提取各个时间节点
                time_fields = ["keywords", "sources", "fetch", "TTFT", "total_time"]
                
                for field in time_fields:
                    if field in time_cost:
                        try:
                            timing_info[field] = float(time_cost[field])
                        except (ValueError, TypeError):
                            pass
        
        return timing_info

    def format_timing_summary(self, timing_info: Dict[str, float]) -> str:
        """格式化时间信息摘要"""
        if not timing_info:
            return ""
        
        summary_parts = []
        for field, duration in timing_info.items():
            if duration > 0.01:  # 只显示有意义的时间（>0.01s）
                summary_parts.append(f"{field}:{duration:.2f}s")
        
        return f"[{' '.join(summary_parts)}]" if summary_parts else ""

    def test_response_time_performance(self) -> bool:
        """测试1: 响应时间性能测试（10轮）"""
        test_name = "响应时间性能测试"
        start_time = time.time()
        
        # 多样化的测试查询
        test_queries = [
            "北京今天的天气怎么样",
            "上海有什么好吃的餐厅推荐",
            "深圳科技园附近的酒店",
            "广州白云机场到市区怎么走",
            "杭州西湖景区门票价格",
            "成都火锅哪家最正宗",
            "西安兵马俑开放时间",
            "南京夫子庙有什么特色小吃",
            "苏州园林最值得去的是哪个",
            "青岛海边景点推荐"
        ]
        
        response_times = []
        successful_requests = 0
        test_rounds = 10
        all_timing_data = []
        
        print(f"   执行 {test_rounds} 轮性能测试...")
        
        for i in range(test_rounds):
            query = test_queries[i % len(test_queries)]
            
            payload = {
                "engine": "bing",
                "query": query,
                "stream": False,
                "detect": True,
                "k": 5
            }
            
            try:
                req_start = time.time()
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                req_duration = time.time() - req_start
                
                if response.status_code == 200:
                    response_times.append(req_duration)
                    successful_requests += 1
                    
                    # 解析响应中的时间信息
                    try:
                        result = response.json()
                        timing_info = self.extract_timing_info(result)
                        all_timing_data.append(timing_info)
                        sources_count = len(result.get("sources", []))
                        timing_summary = self.format_timing_summary(timing_info)
                        print(f"   第{i+1}轮: {req_duration:.2f}s (来源数量: {sources_count}) {timing_summary}")
                    except Exception as e:
                        print(f"   第{i+1}轮: {req_duration:.2f}s (响应解析失败: {str(e)})")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 生成性能统计报告
        if response_times and all_timing_data:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            success_rate = successful_requests / test_rounds
            
            print(f"\n   📊 性能测试统计报告:")
            print(f"   ================================")
            print(f"   总体响应时间:")
            print(f"     平均: {avg_time:.2f}s, 中位数: {median_time:.2f}s")
            print(f"     最快: {min_time:.2f}s, 最慢: {max_time:.2f}s")
            print(f"     成功率: {success_rate:.1%}")
            
            # 计算各模块的平均耗时
            module_stats = {}
            for timing_info in all_timing_data:
                for field, duration_val in timing_info.items():
                    if field not in module_stats:
                        module_stats[field] = []
                    if duration_val > 0:
                        module_stats[field].append(duration_val)
            
            print(f"\n   各模块平均耗时:")
            sorted_modules = sorted(module_stats.items(),
                                  key=lambda x: mean(x[1]) if x[1] else 0, reverse=True)
            
            for field, durations in sorted_modules:
                if durations:
                    avg_duration = mean(durations)
                    print(f"     {field:15s}: {avg_duration:6.2f}s (出现{len(durations)}次)")
            
            # 性能标准：平均响应时间 < 30秒，成功率 > 80%
            performance_ok = avg_time < 30.0 and success_rate > 0.8
            
            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False

    def test_concurrent_performance(self) -> bool:
        """测试2: 并发性能测试"""
        test_name = "并发性能测试"
        start_time = time.time()
        
        def single_request(query_id: int) -> Dict[str, Any]:
            """单个请求函数"""
            payload = {
                "engine": "bing",
                "query": f"测试查询{query_id}",
                "stream": False,
                "detect": True,
                "k": 3
            }
            
            req_start = time.time()
            try:
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                req_duration = time.time() - req_start
                
                return {
                    "query_id": query_id,
                    "success": response.status_code == 200,
                    "duration": req_duration,
                    "status_code": response.status_code
                }
            except Exception as e:
                req_duration = time.time() - req_start
                return {
                    "query_id": query_id,
                    "success": False,
                    "duration": req_duration,
                    "error": str(e)
                }
        
        # 并发测试：5个并发请求
        concurrent_requests = 5
        print(f"   执行 {concurrent_requests} 个并发请求...")
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(single_request, i) for i in range(concurrent_requests)]
            results = []
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                status = "✓" if result["success"] else "✗"
                print(f"   请求{result['query_id']}: {status} {result['duration']:.2f}s")
        
        duration = time.time() - start_time
        
        # 分析并发测试结果
        successful_requests = sum(1 for r in results if r["success"])
        success_rate = successful_requests / concurrent_requests
        avg_concurrent_time = mean([r["duration"] for r in results])
        
        # 并发性能标准：成功率 > 60%，平均响应时间 < 40秒
        performance_ok = success_rate > 0.6 and avg_concurrent_time < 40.0
        
        message = f"并发成功率:{success_rate:.1%} 平均响应时间:{avg_concurrent_time:.2f}s"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_engine_performance_comparison(self) -> bool:
        """测试3: 不同引擎性能对比"""
        test_name = "不同引擎性能对比"
        start_time = time.time()
        
        engines = ["bing", "google", "tencent"]
        engine_results = {}
        
        for engine in engines:
            payload = {
                "engine": engine,
                "query": "中国的首都是哪里",
                "stream": False,
                "detect": True,
                "k": 3
            }
            
            try:
                req_start = time.time()
                response = requests.post(
                    self.search_url,
                    headers=self.headers,
                    json=payload,
                    timeout=45
                )
                req_duration = time.time() - req_start
                
                engine_results[engine] = {
                    "success": response.status_code == 200,
                    "duration": req_duration,
                    "status_code": response.status_code
                }
                
                if response.status_code == 200:
                    result = response.json()
                    engine_results[engine]["has_answer"] = bool(result.get("answer"))
                    engine_results[engine]["sources_count"] = len(result.get("sources", []))
                
                print(f"   {engine}引擎: {req_duration:.2f}s (状态码: {response.status_code})")
                
            except Exception as e:
                req_duration = time.time() - req_start
                engine_results[engine] = {
                    "success": False,
                    "duration": req_duration,
                    "error": str(e)
                }
                print(f"   {engine}引擎: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        # 分析引擎性能
        successful_engines = sum(1 for r in engine_results.values() if r["success"])
        avg_engine_time = mean([r["duration"] for r in engine_results.values() if r["success"]])
        
        # 引擎性能标准：至少一半引擎成功
        performance_ok = successful_engines >= len(engines) // 2
        
        message = f"成功引擎数:{successful_engines}/{len(engines)} 平均响应时间:{avg_engine_time:.2f}s"
        self.log_test_result(test_name, performance_ok, message, duration)
        return performance_ok

    def test_cache_performance(self) -> bool:
        """测试4: 缓存性能测试"""
        test_name = "缓存性能测试"
        start_time = time.time()
        
        query = "北京天安门广场"
        
        # 第一次请求（无缓存）
        payload_no_cache = {
            "engine": "bing",
            "query": query,
            "stream": False,
            "detect": True,
            "k": 3,
            "use_search_cache": False
        }
        
        # 第二次请求（使用缓存）
        payload_with_cache = {
            "engine": "bing",
            "query": query,
            "stream": False,
            "detect": True,
            "k": 3,
            "use_search_cache": True
        }
        
        try:
            # 无缓存请求
            req_start = time.time()
            response1 = requests.post(
                self.search_url,
                headers=self.headers,
                json=payload_no_cache,
                timeout=60
            )
            no_cache_duration = time.time() - req_start
            
            # 使用缓存请求
            req_start = time.time()
            response2 = requests.post(
                self.search_url,
                headers=self.headers,
                json=payload_with_cache,
                timeout=60
            )
            cache_duration = time.time() - req_start
            
            duration = time.time() - start_time
            
            if response1.status_code == 200 and response2.status_code == 200:
                cache_improvement = (no_cache_duration - cache_duration) / no_cache_duration * 100
                print(f"   无缓存请求: {no_cache_duration:.2f}s")
                print(f"   缓存请求: {cache_duration:.2f}s")
                print(f"   缓存提升: {cache_improvement:.1f}%")
                
                # 缓存性能标准：缓存请求应该更快或相近
                performance_ok = cache_duration <= no_cache_duration * 1.2
                
                message = f"无缓存:{no_cache_duration:.2f}s 缓存:{cache_duration:.2f}s 提升:{cache_improvement:.1f}%"
                self.log_test_result(test_name, performance_ok, message, duration)
                return performance_ok
            else:
                self.log_test_result(test_name, False, f"请求失败 (状态码: {response1.status_code}, {response2.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"缓存测试异常: {str(e)}", duration)
            return False

    def run_performance_tests(self) -> bool:
        """运行所有性能测试"""
        print("=" * 80)
        print("🚀 Online Search Framework 性能测试 002")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 执行测试序列
        tests = [
            self.test_response_time_performance,
            self.test_concurrent_performance,
            self.test_engine_performance_comparison,
            self.test_cache_performance
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)

        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests

        print("📊 性能测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if overall_success:
            print("🎉 性能测试全部通过！服务性能良好")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分性能测试通过，服务性能基本满足要求")
        else:
            print("⚠️  多项性能测试失败，请优化服务性能")

        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"

    performance_test = OnlineSearchPerformanceTest(base_url)
    success = performance_test.run_performance_tests()

    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
